# CureOx Healthcare Software Website

A comprehensive, modern website for CureOx, a healthcare software solutions provider with 20 years of experience in medical software development. The website showcases innovative products for dental clinics, pharmacies, medical practices, and patients.

## 🚀 NEW: API Integration Implementation

This project now includes a complete authentication and payment system integration with the CureOx backend APIs.

### Implemented APIs

#### 1. Login API
- **Endpoint**: `https://srv701689.hstgr.cloud/api/auth/login`
- **Method**: POST
- **Data**: `{email, password}`
- **Implementation**: `js/auth.js` - `handleLogin()`

#### 2. Register API
- **Endpoint**: `https://srv701689.hstgr.cloud/api/auth/register`
- **Method**: POST
- **Data**: `{name, email, mobile, password}`
- **Implementation**: `js/auth.js` - `handleSignup()`

#### 3. Email Verification API
- **Endpoint**: `https://srv701689.hstgr.cloud/api/auth/verify-email`
- **Method**: POST
- **Headers**: `Authorization: Bearer {token}`
- **Data**: `{otp: "123456"}`
- **Implementation**: `js/auth.js` - `handleEmailVerification()`

#### 4. Payment API
- **Endpoint**: `https://srv701689.hstgr.cloud/api/payment`
- **Method**: POST
- **Headers**: `Authorization: Bearer {token}`, `lang: {locale}`
- **Data**: `{product_id, months: 1, package_id: 1}`
- **Implementation**: `js/payment.js` - `handlePayment()`

### New Files Added
```
├── js/
│   ├── api.js          # API client and utility functions
│   ├── auth.js         # Authentication handlers
│   └── payment.js      # Payment processing
├── email-verification.html  # Email verification page
└── payment-status.html      # Payment status page
```

### Authentication Flow
1. **Registration**: User creates account with name, email, mobile, password
2. **Email Verification**: User enters OTP (demo: 123456) to verify email
3. **Login**: User logs in with email/password
4. **Token Management**: JWT tokens stored in localStorage
5. **Protected Routes**: Checkout requires authentication

### Payment Flow
1. **Product Selection**: User selects product from pricing page
2. **Authentication Check**: Redirects to login if not authenticated
3. **Checkout Form**: User fills billing and payment information
4. **Payment API**: Processes payment and returns payment URL
5. **External Payment**: Opens payment gateway in new tab
6. **Status Page**: Shows payment status and details

### Demo Usage
- **Email Verification OTP**: `123456`
- All forms include validation and error handling
- Payment flow redirects to external payment gateway

## Overview

CureOx provides cutting-edge software solutions designed to streamline workflows and improve patient care across the healthcare ecosystem. The website features a professional, responsive design with advanced theming capabilities, interactive demonstrations, and comprehensive information about our healthcare software products and services.

## Products

- **Dental Clinic Management System**: Complete practice management solution featuring appointment scheduling, treatment session tracking, and image/report management for dental practices
- **Pharmacy Management Program**: Comprehensive inventory management and prescription tracking system enabling pharmacies to manage medications, process orders, and handle electronic prescriptions with delivery services
- **Medical Clinic Management Program**: Fully integrated solution for streamlining administrative and medical operations including appointment scheduling, medical records management, billing, and medical report generation
- **Patient-Centric App**: Mobile application empowering patients to book appointments, track medical history, order medications online, and receive prescriptions directly from doctors to pharmacies

## Key Features

### Core Functionality
- Responsive design optimized for mobile, tablet, and desktop devices
- Advanced dark/light theme system with user preference persistence
- Interactive product demonstrations with live feature walkthroughs
- Comprehensive blog system with search, filtering, and categorization
- Contact forms and demo request functionality
- FAQ section with detailed product information
- Professional navigation with smooth scrolling and mobile menu

### Advanced Features
- AOS (Animate On Scroll) library integration for smooth animations
- Particle effects and dynamic visual elements
- Theme toggle with system preference detection
- User preference tracking and localStorage integration
- Lazy loading for optimized performance
- SEO-optimized structure and meta tags
- Professional gradient text effects and custom styling

## Technologies Used

### Frontend Framework & Styling
- **HTML5** - Semantic markup and modern web standards
- **Tailwind CSS 2.2.19** - Utility-first CSS framework for responsive design
- **Custom CSS** - Advanced theming system with CSS variables for dark/light modes

### JavaScript Libraries & Features
- **Vanilla JavaScript** - Core interactivity and DOM manipulation
- **AOS (Animate On Scroll) 2.3.1** - Smooth scroll-triggered animations
- **LocalStorage API** - User preference persistence and theme settings
- **Intersection Observer API** - Lazy loading and performance optimization

### Design & UX
- **Responsive Design** - Mobile-first approach with breakpoint optimization
- **Dark/Light Theme System** - Advanced theming with system preference detection
- **Particle Effects** - Custom JavaScript animations for visual enhancement
- **Gradient Text Effects** - CSS-based gradient styling for branding

## Website Structure

### Main Pages
- **Home** (`index.html`) - Main landing page with company overview, services, products, and blog preview
- **Product Pages** - Detailed information for each healthcare software solution:
  - `product1.html` - Dental Clinic Management System
  - `product2.html` - Pharmacy Management Program
  - `product3.html` - Medical Clinic Management Program
  - `product4.html` - Patient-Centric App

### Interactive Features
- **Blog System** (`blog-redesign.html`) - Healthcare technology insights with search and filtering
- **Article Pages** (`article.html`) - Individual blog articles with dynamic content loading
- **Interactive Demos** - Live product demonstrations:
  - `demos/dentalpro-demo.html` - Dental clinic software demo
  - `demos/pharmtrack-demo.html` - Pharmacy management demo
  - `demos/medoffice-demo.html` - Medical clinic demo
  - `demos/patient-app-demo.html` - Patient app demo

### Support & Business Pages
- **Contact** (`contact.html`) - Contact information and inquiry forms
- **Demo Request** (`demo.html`) - Personalized demo request system
- **Pricing** (`pricing.html`) - Subscription plans and pricing information
- **Support** (`support.html`) - Help resources and documentation
- **Legal** (`terms.html`, `privacy.html`) - Terms of service and privacy policy
- **Authentication** (`login.html`, `signup.html`) - User account management
- **Checkout** (`checkout.html`) - Secure payment processing

## Project Structure

```
cureox-christian13/
├── index.html                    # Main landing page with hero, services, products, and blog
├── script.js                     # Main JavaScript file with core functionality
│
├── Product Pages/
│   ├── product1.html             # Dental Clinic Management System
│   ├── product2.html             # Pharmacy Management Program
│   ├── product3.html             # Medical Clinic Management Program
│   └── product4.html             # Patient-Centric App
│
├── Blog System/
│   ├── blog-redesign.html        # Main blog page with search and filtering
│   ├── blog.html                 # Alternative blog layout
│   └── article.html              # Individual article template
│
├── Interactive Demos/
│   └── demos/
│       ├── dentalpro-demo.html   # Dental clinic software demo
│       ├── pharmtrack-demo.html  # Pharmacy management demo
│       ├── medoffice-demo.html   # Medical clinic demo
│       └── patient-app-demo.html # Patient app demo
│
├── Business Pages/
│   ├── contact.html              # Contact information and forms
│   ├── demo.html                 # Demo request system
│   ├── pricing.html              # Pricing and subscription plans
│   ├── support.html              # Help and documentation
│   ├── checkout.html             # Payment processing
│   ├── login.html                # User authentication
│   └── signup.html               # User registration
│
├── Legal/
│   ├── terms.html                # Terms of service
│   └── privacy.html              # Privacy policy
│
├── Assets/
│   ├── styles/
│   │   └── theme.css             # Advanced theming system with CSS variables
│   ├── js/
│   │   ├── user-preferences.js   # User preference tracking and localStorage
│   │   ├── demo-interactions.js  # Interactive demo functionality
│   │   ├── article.js            # Article page dynamic content
│   │   ├── blog-features.js      # Blog functionality
│   │   └── blog-features-redesign.js # Enhanced blog features
│   └── images/                   # Image assets and media files
       ├── HeroSection.jpg        # Main hero background
       ├── Dentist.jpg           # Dental product imagery
       ├── Doctor.jpg            # Medical product imagery
       ├── Pharmacy.png          # Pharmacy product imagery
       ├── Patient.jpg           # Patient app imagery
       └── blog-*.jpg/webp       # Blog article images
```

## How to Run/View the Website

### Direct File Access
- You can also open `index.html` directly in your browser, but some features (like theme persistence and dynamic content) may not work properly without a local server

### Dependencies
- **No build process required** - The website uses CDN links for external dependencies
- **External CDN Dependencies**:
  - Tailwind CSS 2.2.19
  - AOS (Animate On Scroll) 2.3.1
- **All other functionality** is implemented with vanilla JavaScript and custom CSS

## Browser Compatibility
- **Modern browsers** (Chrome 80+, Firefox 75+, Safari 13+, Edge 80+)
- **Mobile responsive** design tested on iOS and Android devices
- **JavaScript required** for full functionality (theme switching, animations, interactive demos)

## Key Features in Action

### Theme System
- **Automatic detection** of system dark/light mode preference
- **Manual toggle** available in navigation bar
- **Persistent storage** of user preference across sessions
- **Smooth transitions** between themes

### Interactive Elements
- **Scroll-triggered animations** using AOS library
- **Particle effects** on hero section
- **Hover effects** on cards and buttons
- **Mobile-responsive** navigation with hamburger menu

### Blog System
- **Search functionality** for articles and topics
- **Category filtering** for content organization
- **Featured articles** with enhanced styling
- **Author profiles** and article metadata

## Development Notes
- **CSS Variables** used extensively for theming
- **Mobile-first** responsive design approach
- **Performance optimized** with lazy loading and efficient animations
- **SEO-friendly** structure with semantic HTML

## License

Copyright © 2025 CureOx. All rights reserved.

