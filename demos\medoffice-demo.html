<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Medical Clinic Management Program Interactive Demo - CureOx</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
  <link href="../styles/theme.css" rel="stylesheet">
  <style>
    .demo-screen {
      transition: all 0.5s ease;
      opacity: 0;
      transform: translateY(20px);
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
    .demo-screen.active {
      opacity: 1;
      transform: translateY(0);
      z-index: 10;
    }
    .feature-button {
      transition: all 0.3s ease;
    }
    .feature-button.active {
      background-color: #3b82f6;
      color: white;
      /* Removed transform scale to prevent movement */
    }
    .demo-container {
      position: relative;
      height: 500px;
      overflow: hidden;
      border-radius: 0.5rem;
    }

    /* Feature Navigation Button Styling for Light/Dark Mode */
    .feature-button {
      transition: all 0.3s ease;
      color: white; /* Default text color for dark mode */
    }

    .feature-button.active {
      background-color: #3b82f6 !important;
      color: white !important;
    }

    /* Light Mode Styling for Feature Buttons */
    .light-mode .feature-button {
      background-color: white !important;
      color: #1e293b !important; /* Dark text for light background */
      border: 1px solid #e2e8f0 !important;
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
    }

    .light-mode .feature-button:hover {
      background-color: #f8fafc !important;
      border-color: #3b82f6 !important;
      color: #3b82f6 !important;
    }

    .light-mode .feature-button.active {
      background-color: #3b82f6 !important;
      color: white !important;
      border-color: #3b82f6 !important;
    }

    /* Dark Mode Styling for Feature Buttons */
    .dark-mode .feature-button {
      background-color: #374151 !important; /* bg-gray-700 */
      color: white !important;
      border: 1px solid transparent !important;
    }

    .dark-mode .feature-button:hover {
      background-color: #3b82f6 !important;
      color: white !important;
    }

    .dark-mode .feature-button.active {
      background-color: #3b82f6 !important;
      color: white !important;
    }

  </style>
</head>
<body>
  <!-- Navigation -->
  <nav class="bg-gray-900 bg-opacity-90 backdrop-filter backdrop-blur-lg fixed w-full z-10 shadow-lg">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <a href="../index.html" class="text-2xl font-bold text-gradient flex items-center">
              <span class="mr-2">CureOx</span>
              <svg class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </a>
          </div>
        </div>
        <div class="hidden md:block">
          <div class="ml-10 flex items-baseline space-x-4">
            <a href="../index.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Home</a>
            <a href="../product1.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Dental Clinic Management System</a>
            <a href="../product2.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Pharmacy Management Program</a>
            <a href="../product3.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Medical Clinic Management Program</a>
            <a href="../product4.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Patient-Centric App</a>
            <a href="../demo.html" class="ml-4 bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded-md text-sm font-medium transition-all">Request Demo</a>
          </div>
        </div>
        <div class="hidden md:flex items-center space-x-4">
          <button id="theme-toggle" class="text-gray-300 hover:text-white focus:outline-none">
            <svg id="moon-icon" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
            </svg>
            <svg id="sun-icon" class="h-5 w-5 hidden" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
        <div class="-mr-2 flex md:hidden">
          <button id="mobile-menu-button" type="button" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none focus:bg-gray-700 focus:text-white">
            <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
              <path class="inline-flex" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
    </div>
    <div id="mobile-menu" class="hidden md:hidden">
      <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
        <a href="../index.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Home</a>
        <a href="../product1.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Dental Clinic Management System</a>
        <a href="../product2.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Pharmacy Management Program</a>
        <a href="../product3.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Medical Clinic Management Program</a>
        <a href="../product4.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-blue-700 hover:text-white transition-all">Patient-Centric App</a>
        <a href="../demo.html" class="mt-2 w-full bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded-md text-base font-medium transition-all block text-center">Request Demo</a>
        <div class="flex items-center justify-between px-3 py-2">
          <span class="text-gray-300">Dark/Light Mode</span>
          <button id="mobile-theme-toggle" class="text-gray-300 hover:text-white focus:outline-none">
            <svg id="mobile-moon-icon" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
            </svg>
            <svg id="mobile-sun-icon" class="h-5 w-5 hidden" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </nav>

  <!-- Demo Hero Section -->
  <section class="pt-32 pb-10 bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <h1 class="text-4xl md:text-5xl font-bold mb-6" data-aos="fade-up">Medical Clinic Management <span class="text-gradient">Interactive Demo</span></h1>
        <p class="text-xl text-gray-300 mb-8 max-w-3xl mx-auto" data-aos="fade-up" data-aos-delay="100">Experience the power of our Medical Clinic Management Program firsthand. Explore key features and see how our software can transform your medical practice.</p>
      </div>
    </div>
  </section>

  <!-- Interactive Demo Section -->
  <section class="py-16 bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <!-- Feature Navigation -->
        <div class="bg-gray-800 p-6 rounded-lg shadow-lg" data-aos="fade-up">
          <h2 class="text-2xl font-bold mb-6">Key Features</h2>
          <div class="space-y-4">
            <button class="feature-button w-full text-left px-4 py-3 rounded-md bg-gray-700 hover:bg-blue-600 transition-all active" data-feature="ehr">
              Electronic Health Records
            </button>
            <button class="feature-button w-full text-left px-4 py-3 rounded-md bg-gray-700 hover:bg-blue-600 transition-all" data-feature="scheduling">
              Practice Scheduling
            </button>
            <button class="feature-button w-full text-left px-4 py-3 rounded-md bg-gray-700 hover:bg-blue-600 transition-all" data-feature="telemedicine">
              Telemedicine
            </button>
            <button class="feature-button w-full text-left px-4 py-3 rounded-md bg-gray-700 hover:bg-blue-600 transition-all" data-feature="billing">
              Billing & Claims
            </button>
            <button class="feature-button w-full text-left px-4 py-3 rounded-md bg-gray-700 hover:bg-blue-600 transition-all" data-feature="analytics">
              Practice Analytics
            </button>
          </div>
          <div class="mt-8">
            <p class="text-gray-300 mb-4">Want to see more? Schedule a personalized demo with our team.</p>
            <a href="../demo.html" class="block w-full bg-blue-600 hover:bg-blue-500 text-white px-4 py-3 rounded-md text-center font-medium transition-all">Request Full Demo</a>
          </div>
        </div>

        <!-- Interactive Demo Display -->
        <div class="md:col-span-2" data-aos="fade-up" data-aos-delay="100">
          <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
            <div class="demo-container bg-gray-900" style="transform: rotate(0deg) !important; perspective: none !important; transition: none !important; box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.1), 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important; pointer-events: auto !important;">
              <!-- EHR Screen -->
              <div class="demo-screen active" id="ehr-screen">
                <img src="../images/medoffice-ehr-demo.jpg" alt="Electronic Health Records" class="w-full h-full object-cover">

              </div>

              <!-- Scheduling Screen -->
              <div class="demo-screen" id="scheduling-screen">
                <img src="../images/medoffice-scheduling-demo.jpg" alt="Practice Scheduling" class="w-full h-full object-cover">

              </div>

              <!-- Telemedicine Screen -->
              <div class="demo-screen" id="telemedicine-screen">
                <img src="../images/medoffice-telemedicine-demo.jpg" alt="Telemedicine" class="w-full h-full object-cover">

              </div>

              <!-- Billing Screen -->
              <div class="demo-screen" id="billing-screen">
                <img src="../images/medoffice-billing-demo.jpg" alt="Billing & Claims" class="w-full h-full object-cover">

              </div>

              <!-- Analytics Screen -->
              <div class="demo-screen" id="analytics-screen">
                <img src="../images/medoffice-analytics-demo.jpg" alt="Practice Analytics" class="w-full h-full object-cover">

              </div>
            </div>
            <div class="mt-6">
              <h3 class="text-xl font-bold mb-2" id="feature-title">Electronic Health Records</h3>
              <p class="text-gray-300" id="feature-description">Comprehensive electronic health records with intuitive charting, e-prescribing, and lab integration. Access patient information securely from anywhere with our cloud-based solution.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Call to Action -->
  <section class="py-16 bg-gradient-to-r from-blue-900 to-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h2 class="text-3xl font-bold mb-6" data-aos="fade-up">Ready to transform your medical practice?</h2>
      <p class="text-xl text-gray-300 mb-8 max-w-3xl mx-auto" data-aos="fade-up" data-aos-delay="100">Join thousands of healthcare providers who have streamlined their practice with our Medical Clinic Management Program.</p>
      <div class="flex flex-col sm:flex-row justify-center gap-4" data-aos="fade-up" data-aos-delay="200">
        <a href="../demo.html" class="bg-blue-600 hover:bg-blue-500 text-white px-8 py-3 rounded-md font-medium transition-all">Request Full Demo</a>
        <a href="../pricing.html" class="bg-gray-700 hover:bg-gray-600 text-white px-8 py-3 rounded-md font-medium transition-all">View Pricing</a>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gradient-to-r from-gray-900 to-blue-900 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <a href="../index.html" class="text-2xl font-bold text-gradient">CureOx</a>
        <p class="mt-4 text-gray-300">Transforming healthcare with innovative software solutions.</p>
      </div>
      <div class="border-t border-gray-700 mt-12 pt-8 text-center text-gray-400">
        <p>© 2023 CureOx. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <script src="../script.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Make all cards steady
      const allCards = document.querySelectorAll('.bg-gray-800.rounded-lg, .demo-container, .md\\:col-span-2');
      allCards.forEach(card => {
        card.style.transform = 'none';
        card.style.transition = 'none';
        card.style.perspective = 'none';
        card.style.boxShadow = '0 0 15px 0 rgba(0, 0, 0, 0.1), 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';

        // Remove any event listeners that might cause movement
        const newCard = card.cloneNode(true);
        card.parentNode.replaceChild(newCard, card);
      });

      // Initialize AOS
      AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: false,
        mirror: true
      });

      // Demo feature navigation
      const featureButtons = document.querySelectorAll('.feature-button');
      const featureTitle = document.getElementById('feature-title');
      const featureDescription = document.getElementById('feature-description');

      // Feature descriptions
      const featureDescriptions = {
        'ehr': 'Comprehensive electronic health records with intuitive charting, e-prescribing, and lab integration. Access patient information securely from anywhere with our cloud-based solution.',
        'scheduling': 'Streamline your practice scheduling with multi-provider calendars, automated reminders, and online patient self-scheduling. Reduce no-shows and optimize your clinic\'s efficiency.',
        'telemedicine': 'Deliver virtual care with secure video consultations, virtual waiting rooms, and seamless EHR integration. Expand your practice reach and improve patient access to care.',
        'billing': 'Simplify your revenue cycle with automated claim submission, real-time eligibility verification, and integrated payment processing. Reduce denials and accelerate reimbursements.',
        'analytics': 'Make data-driven decisions with comprehensive practice analytics. Monitor financial performance, patient demographics, and provider productivity to optimize your practice.'
      };

      featureButtons.forEach(button => {
        button.addEventListener('click', function() {
          // Remove active class from all buttons
          featureButtons.forEach(btn => {
            btn.classList.remove('active');
            btn.classList.remove('bg-blue-600');
            btn.classList.add('bg-gray-700');
          });

          // Add active class to clicked button
          this.classList.add('active');
          this.classList.add('bg-blue-600');
          this.classList.remove('bg-gray-700');

          // Get feature ID
          const featureId = this.getAttribute('data-feature');

          // Hide all screens
          document.querySelectorAll('.demo-screen').forEach(screen => {
            screen.classList.remove('active');
          });

          // Show selected screen
          document.getElementById(`${featureId}-screen`).classList.add('active');

          // Update feature title and description
          featureTitle.textContent = this.textContent.trim();
          featureDescription.textContent = featureDescriptions[featureId];
        });
      });


    });
  </script>
</body>
</html>

