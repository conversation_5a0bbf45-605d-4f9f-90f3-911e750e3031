<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Email Verification - CureOx Healthcare Software</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <link href="styles/theme.css" rel="stylesheet">
</head>
<body>
  <!-- Navigation -->
  <nav class="bg-gray-900 bg-opacity-90 backdrop-filter backdrop-blur-lg fixed w-full z-10 shadow-lg">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <a href="index.html" class="text-2xl font-bold text-gradient flex items-center">
              <span class="mr-2">CureOx</span>
              <svg class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </a>
          </div>
        </div>
        <div class="hidden md:block">
          <div class="flex items-center space-x-4">
            <button id="theme-toggle" class="text-gray-300 hover:text-white focus:outline-none">
              <svg id="moon-icon" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
              </svg>
              <svg id="sun-icon" class="h-5 w-5 hidden" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
              </svg>
            </button>
            <a href="contact.html" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-all">
              Contact Us
            </a>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- Email Verification Section -->
  <section class="pt-32 pb-16 bg-gray-900 min-h-screen">
    <div class="max-w-md mx-auto px-4 sm:px-6 lg:px-8">
      <div class="bg-gray-800 p-8 rounded-lg shadow-lg">
        <div class="text-center mb-8">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4">
            <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <h1 class="text-3xl font-bold mb-2">Verify Your Email</h1>
          <p class="text-gray-300">We've sent a verification code to your email address. Please enter it below to complete your registration.</p>
        </div>
        
        <form id="verify-form" class="space-y-6">
          <div>
            <label for="otp" class="block text-sm font-medium text-gray-300 mb-1">Verification Code</label>
            <input type="text" id="otp" name="otp" maxlength="6" placeholder="123456" required class="w-full px-4 py-2 rounded-md bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 text-center text-lg tracking-widest">
            <p class="text-sm text-gray-400 mt-1">Enter the 6-digit code sent to your email</p>
          </div>
          
          <div>
            <button type="submit" class="w-full bg-blue-600 hover:bg-blue-500 text-white py-2 px-4 rounded-md font-medium transition-all">Verify Email</button>
          </div>
        </form>
        
        <div class="mt-6 text-center">
          <p class="text-gray-300">Didn't receive the code? 
            <button id="resend-code" class="text-blue-400 hover:text-blue-300 underline">Resend Code</button>
          </p>
          <p class="text-gray-300 mt-2">
            <a href="login.html" class="text-blue-400 hover:text-blue-300">Back to Login</a>
          </p>
        </div>

        <!-- Demo Notice -->
        <div class="mt-8 p-4 bg-yellow-500 bg-opacity-20 border border-yellow-400 rounded-md">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <div>
              <p class="text-yellow-300 text-sm font-medium">Demo Mode</p>
              <p class="text-yellow-200 text-sm">Use verification code: <strong>123456</strong></p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gradient-to-r from-gray-900 to-blue-900 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <a href="index.html" class="text-2xl font-bold text-gradient">CureOx</a>
        <p class="mt-2 text-gray-300">Transforming healthcare with innovative software solutions.</p>
      </div>
      <div class="border-t border-gray-700 mt-6 pt-6 text-center text-gray-400">
        <p>© 2023 CureOx. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="js/api.js"></script>
  <script src="js/auth.js"></script>
  <script src="script.js"></script>
  <script src="js/user-preferences.js"></script>
  
  <script>
    // Additional functionality for email verification page
    document.addEventListener('DOMContentLoaded', function() {
      // Auto-format OTP input
      const otpInput = document.getElementById('otp');
      if (otpInput) {
        otpInput.addEventListener('input', function(e) {
          // Only allow numbers
          this.value = this.value.replace(/[^0-9]/g, '');
          
          // Auto-submit when 6 digits are entered
          if (this.value.length === 6) {
            // Small delay to show the complete code
            setTimeout(() => {
              document.getElementById('verify-form').dispatchEvent(new Event('submit'));
            }, 500);
          }
        });
      }

      // Resend code functionality
      const resendButton = document.getElementById('resend-code');
      if (resendButton) {
        resendButton.addEventListener('click', function() {
          // In a real implementation, this would call an API to resend the code
          UIUtils.showSuccess('Verification code has been resent to your email.', document.querySelector('.bg-gray-800'));
          
          // Disable button temporarily
          this.disabled = true;
          this.textContent = 'Code Sent';
          
          setTimeout(() => {
            this.disabled = false;
            this.textContent = 'Resend Code';
          }, 30000); // 30 seconds cooldown
        });
      }
    });
  </script>
</body>
</html>
