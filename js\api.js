// API Configuration and Utility Functions
const API_CONFIG = {
    BASE_URL: 'https://srv701689.hstgr.cloud/api',
    ENDPOINTS: {
        LOGIN: '/auth/login',
        REGISTER: '/auth/register',
        VERIFY_EMAIL: '/auth/verify-email',
        PAYMENT: '/payment'
    }
};

// API Utility Class
class APIClient {
    constructor() {
        this.baseURL = API_CONFIG.BASE_URL;
    }

    // Get stored token
    getToken() {
        return localStorage.getItem('authToken');
    }

    // Store token
    setToken(token) {
        localStorage.setItem('authToken', token);
    }

    // Remove token
    removeToken() {
        localStorage.removeItem('authToken');
    }

    // Get stored user data
    getUserData() {
        const userData = localStorage.getItem('userData');
        return userData ? JSON.parse(userData) : null;
    }

    // Store user data
    setUserData(userData) {
        localStorage.setItem('userData', JSON.stringify(userData));
    }

    // Remove user data
    removeUserData() {
        localStorage.removeItem('userData');
    }

    // Generic API request method
    async makeRequest(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const token = this.getToken();
        
        const defaultHeaders = {
            'Content-Type': 'application/json',
        };

        if (token) {
            defaultHeaders['Authorization'] = `Bearer ${token}`;
        }

        const config = {
            method: 'GET',
            headers: defaultHeaders,
            ...options,
            headers: {
                ...defaultHeaders,
                ...options.headers
            }
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || `HTTP error! status: ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API Request failed:', error);
            throw error;
        }
    }

    // Login API
    async login(email, password) {
        const data = { email, password };
        
        return await this.makeRequest(API_CONFIG.ENDPOINTS.LOGIN, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // Register API
    async register(name, email, mobile, password) {
        const data = { name, email, mobile, password };
        
        return await this.makeRequest(API_CONFIG.ENDPOINTS.REGISTER, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // Verify Email API
    async verifyEmail(otp) {
        const token = this.getToken();
        if (!token) {
            throw new Error('No authentication token found');
        }

        return await this.makeRequest(API_CONFIG.ENDPOINTS.VERIFY_EMAIL, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ otp })
        });
    }

    // Payment API
    async processPayment(productId, months = 1, packageId = 1, locale = 'en') {
        const token = this.getToken();
        if (!token) {
            throw new Error('No authentication token found');
        }

        return await this.makeRequest(API_CONFIG.ENDPOINTS.PAYMENT, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'lang': locale
            },
            body: JSON.stringify({ 
                product_id: productId, 
                months, 
                package_id: packageId 
            })
        });
    }

    // Logout
    logout() {
        this.removeToken();
        this.removeUserData();
        // Redirect to login page
        window.location.href = 'login.html';
    }

    // Check if user is authenticated
    isAuthenticated() {
        return !!this.getToken();
    }
}

// Create global API client instance
window.apiClient = new APIClient();

// Utility functions for UI feedback
const UIUtils = {
    // Show loading state
    showLoading(button, loadingText = 'Loading...') {
        if (button) {
            button.disabled = true;
            button.dataset.originalText = button.innerHTML;
            button.innerHTML = `
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                ${loadingText}
            `;
        }
    },

    // Hide loading state
    hideLoading(button) {
        if (button && button.dataset.originalText) {
            button.disabled = false;
            button.innerHTML = button.dataset.originalText;
            delete button.dataset.originalText;
        }
    },

    // Show error message
    showError(message, container = null) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'bg-red-500 bg-opacity-20 border border-red-400 text-red-300 px-4 py-3 rounded mb-4';
        errorDiv.innerHTML = `
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <span>${message}</span>
            </div>
        `;

        if (container) {
            container.insertBefore(errorDiv, container.firstChild);
        } else {
            document.body.insertBefore(errorDiv, document.body.firstChild);
        }

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 5000);
    },

    // Show success message
    showSuccess(message, container = null) {
        const successDiv = document.createElement('div');
        successDiv.className = 'bg-green-500 bg-opacity-20 border border-green-400 text-green-300 px-4 py-3 rounded mb-4';
        successDiv.innerHTML = `
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <span>${message}</span>
            </div>
        `;

        if (container) {
            container.insertBefore(successDiv, container.firstChild);
        } else {
            document.body.insertBefore(successDiv, document.body.firstChild);
        }

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
            }
        }, 5000);
    },

    // Remove existing messages
    removeMessages(container = null) {
        const parent = container || document.body;
        const messages = parent.querySelectorAll('.bg-red-500, .bg-green-500');
        messages.forEach(msg => {
            if (msg.parentNode) {
                msg.parentNode.removeChild(msg);
            }
        });
    }
};

// Make UIUtils globally available
window.UIUtils = UIUtils;
