// Authentication Handler
class AuthHandler {
    constructor() {
        this.apiClient = window.apiClient;
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Login form handler
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', this.handleLogin.bind(this));
        }

        // Signup form handler
        const signupForm = document.getElementById('signup-form');
        if (signupForm) {
            signupForm.addEventListener('submit', this.handleSignup.bind(this));
        }

        // Email verification form handler
        const verifyForm = document.getElementById('verify-form');
        if (verifyForm) {
            verifyForm.addEventListener('submit', this.handleEmailVerification.bind(this));
        }

        // Auto-fill OTP for demo (remove in production)
        const otpInput = document.getElementById('otp');
        if (otpInput) {
            // Pre-fill with demo OTP
            otpInput.value = '123456';
        }
    }

    // Handle login form submission
    async handleLogin(event) {
        event.preventDefault();

        const form = event.target;
        const formContainer = form.closest('.bg-gray-800');
        const submitButton = form.querySelector('button[type="submit"]');

        // Get form data
        const email = form.querySelector('#email').value.trim();
        const password = form.querySelector('#password').value;

        // Basic validation
        if (!email || !password) {
            UIUtils.showError('Please fill in all required fields.', formContainer);
            return;
        }

        if (!this.isValidEmail(email)) {
            UIUtils.showError('Please enter a valid email address.', formContainer);
            return;
        }

        try {
            // Remove existing messages
            UIUtils.removeMessages(formContainer);

            // Show loading state
            UIUtils.showLoading(submitButton, 'Logging in...');

            // Call login API
            const response = await this.apiClient.login(email, password);

            // Handle successful login
            if (response && response.payload && response.payload.token) {
                // Store token and user data
                this.apiClient.setToken(response.payload.token);
                this.apiClient.setUserData(response.payload);

                UIUtils.showSuccess('Login successful! Redirecting...', formContainer);

                // Check if email verification is needed
                if (response.payload.email_verified === false) {
                    // Redirect to email verification
                    setTimeout(() => {
                        window.location.href = 'email-verification.html';
                    }, 1500);
                } else {
                    // Check for intended checkout URL
                    const intendedUrl = localStorage.getItem('intendedCheckoutUrl');
                    if (intendedUrl) {
                        localStorage.removeItem('intendedCheckoutUrl');
                        setTimeout(() => {
                            window.location.href = intendedUrl;
                        }, 1500);
                    } else {
                        // Redirect to dashboard or home
                        setTimeout(() => {
                            window.location.href = 'index.html';
                        }, 1500);
                    }
                }
            } else {
                throw new Error('Invalid response from server');
            }

        } catch (error) {
            console.error('Login error:', error);
            UIUtils.showError(error.message || 'Login failed. Please try again.', formContainer);
        } finally {
            // Hide loading state
            UIUtils.hideLoading(submitButton);
        }
    }

    // Handle signup form submission
    async handleSignup(event) {
        event.preventDefault();

        const form = event.target;
        const formContainer = form.closest('.bg-gray-800');
        const submitButton = form.querySelector('button[type="submit"]');

        // Get form data
        const firstName = form.querySelector('#first-name').value.trim();
        const lastName = form.querySelector('#last-name').value.trim();
        const email = form.querySelector('#email').value.trim();
        const mobile = form.querySelector('#mobile').value.trim();
        const organization = form.querySelector('#organization').value.trim();
        const password = form.querySelector('#password').value;
        const confirmPassword = form.querySelector('#confirm-password').value;
        const termsAccepted = form.querySelector('#terms').checked;

        // Combine first and last name
        const name = `${firstName} ${lastName}`.trim();

        // Basic validation
        if (!firstName || !lastName || !email || !mobile || !organization || !password || !confirmPassword) {
            UIUtils.showError('Please fill in all required fields.', formContainer);
            return;
        }

        if (!this.isValidEmail(email)) {
            UIUtils.showError('Please enter a valid email address.', formContainer);
            return;
        }

        if (password !== confirmPassword) {
            UIUtils.showError('Passwords do not match.', formContainer);
            return;
        }

        if (password.length < 6) {
            UIUtils.showError('Password must be at least 6 characters long.', formContainer);
            return;
        }

        if (!termsAccepted) {
            UIUtils.showError('Please accept the Terms of Service and Privacy Policy.', formContainer);
            return;
        }

        try {
            // Remove existing messages
            UIUtils.removeMessages(formContainer);

            // Show loading state
            UIUtils.showLoading(submitButton, 'Creating account...');

            // Call register API
            const response = await this.apiClient.register(name, email, mobile, password);

            // Handle successful registration
            if (response && response.payload && response.payload.token) {
                // Store token and user data
                this.apiClient.setToken(response.payload.token);
                this.apiClient.setUserData(response.payload);

                UIUtils.showSuccess('Account created successfully! Please verify your email.', formContainer);

                // Redirect to email verification
                setTimeout(() => {
                    window.location.href = 'email-verification.html';
                }, 2000);
            } else {
                throw new Error('Invalid response from server');
            }

        } catch (error) {
            console.error('Registration error:', error);
            UIUtils.showError(error.message || 'Registration failed. Please try again.', formContainer);
        } finally {
            // Hide loading state
            UIUtils.hideLoading(submitButton);
        }
    }

    // Handle email verification
    async handleEmailVerification(event) {
        event.preventDefault();

        const form = event.target;
        const formContainer = form.closest('.bg-gray-800');
        const submitButton = form.querySelector('button[type="submit"]');

        // Get OTP
        const otp = form.querySelector('#otp').value.trim();

        // Basic validation
        if (!otp) {
            UIUtils.showError('Please enter the verification code.', formContainer);
            return;
        }

        if (otp.length !== 6) {
            UIUtils.showError('Verification code must be 6 digits.', formContainer);
            return;
        }

        try {
            // Remove existing messages
            UIUtils.removeMessages(formContainer);

            // Show loading state
            UIUtils.showLoading(submitButton, 'Verifying...');

            // Call verify email API
            const response = await this.apiClient.verifyEmail(otp);

            // Handle successful verification
            UIUtils.showSuccess('Email verified successfully! Redirecting to dashboard...', formContainer);

            // Update user data to mark email as verified
            const userData = this.apiClient.getUserData();
            if (userData) {
                userData.email_verified = true;
                this.apiClient.setUserData(userData);
            }

            // Check for intended checkout URL
            const intendedUrl = localStorage.getItem('intendedCheckoutUrl');
            if (intendedUrl) {
                localStorage.removeItem('intendedCheckoutUrl');
                setTimeout(() => {
                    window.location.href = intendedUrl;
                }, 2000);
            } else {
                // Redirect to dashboard or home
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 2000);
            }

        } catch (error) {
            console.error('Email verification error:', error);
            UIUtils.showError(error.message || 'Verification failed. Please try again.', formContainer);
        } finally {
            // Hide loading state
            UIUtils.hideLoading(submitButton);
        }
    }

    // Email validation helper
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Check authentication status on page load
    checkAuthStatus() {
        const currentPage = window.location.pathname.split('/').pop();
        const isAuthenticated = this.apiClient.isAuthenticated();
        const userData = this.apiClient.getUserData();

        // Redirect logic based on authentication status
        if (isAuthenticated && userData) {
            // If user is on login/signup pages but already authenticated
            if (currentPage === 'login.html' || currentPage === 'signup.html') {
                if (userData.email_verified === false) {
                    window.location.href = 'email-verification.html';
                } else {
                    window.location.href = 'index.html';
                }
            }
        } else {
            // If user is not authenticated and trying to access protected pages
            const protectedPages = ['email-verification.html', 'checkout.html'];
            if (protectedPages.includes(currentPage)) {
                window.location.href = 'login.html';
            }
        }
    }
}

// Initialize authentication handler when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const authHandler = new AuthHandler();
    authHandler.checkAuthStatus();

    // Make authHandler globally available for debugging
    window.authHandler = authHandler;
});
