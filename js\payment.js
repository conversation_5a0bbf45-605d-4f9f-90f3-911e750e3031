// Payment Handler
class PaymentHandler {
    constructor() {
        this.apiClient = window.apiClient;
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Checkout form handler
        const checkoutForm = document.getElementById('checkout-form');
        if (checkoutForm) {
            checkoutForm.addEventListener('submit', this.handlePayment.bind(this));
        }

        // Initialize product details on checkout page
        if (window.location.pathname.includes('checkout.html')) {
            this.initializeCheckoutPage();
        }
    }

    // Initialize checkout page with product details
    initializeCheckoutPage() {
        // Check if user is authenticated
        if (!this.apiClient.isAuthenticated()) {
            // Redirect to login with return URL
            const returnUrl = encodeURIComponent(window.location.href);
            window.location.href = `login.html?return=${returnUrl}`;
            return;
        }

        // Get product from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const productId = urlParams.get('product');
        
        if (!productId) {
            UIUtils.showError('No product selected. Redirecting to pricing page...');
            setTimeout(() => {
                window.location.href = 'pricing.html';
            }, 2000);
            return;
        }

        // Store product ID for payment processing
        this.selectedProductId = productId;
    }

    // Handle payment form submission
    async handlePayment(event) {
        event.preventDefault();
        
        const form = event.target;
        const formContainer = form.closest('.bg-gray-800');
        const submitButton = form.querySelector('button[type="submit"]');
        
        // Get form data
        const formData = this.getFormData(form);
        
        // Validate form data
        if (!this.validatePaymentForm(formData, formContainer)) {
            return;
        }

        try {
            // Remove existing messages
            UIUtils.removeMessages(formContainer);
            
            // Show loading state
            UIUtils.showLoading(submitButton, 'Processing payment...');

            // Get product ID from URL or stored value
            const urlParams = new URLSearchParams(window.location.search);
            const productId = urlParams.get('product') || this.selectedProductId;
            
            if (!productId) {
                throw new Error('No product selected for payment');
            }

            // Get locale from URL or default to 'en'
            const locale = urlParams.get('locale') || 'en';

            // Call payment API
            const response = await this.apiClient.processPayment(productId, 1, 1, locale);

            // Handle successful payment API response
            if (response && response.payment_url) {
                UIUtils.showSuccess('Payment request processed! Redirecting to payment gateway...', formContainer);
                
                // Store payment details for reference
                this.storePaymentDetails(formData, productId, response);
                
                // Redirect to payment URL
                setTimeout(() => {
                    window.open(response.payment_url, '_blank');
                    // Also redirect current page to a thank you or status page
                    window.location.href = 'payment-status.html';
                }, 2000);
            } else {
                throw new Error('Invalid payment response from server');
            }

        } catch (error) {
            console.error('Payment error:', error);
            UIUtils.showError(error.message || 'Payment processing failed. Please try again.', formContainer);
        } finally {
            // Hide loading state
            UIUtils.hideLoading(submitButton);
        }
    }

    // Get form data
    getFormData(form) {
        return {
            firstName: form.querySelector('#first-name').value.trim(),
            lastName: form.querySelector('#last-name').value.trim(),
            email: form.querySelector('#email').value.trim(),
            phone: form.querySelector('#phone').value.trim(),
            company: form.querySelector('#company').value.trim(),
            address: form.querySelector('#address').value.trim(),
            city: form.querySelector('#city').value.trim(),
            state: form.querySelector('#state').value.trim(),
            zip: form.querySelector('#zip').value.trim(),
            country: form.querySelector('#country').value,
            cardName: form.querySelector('#card-name').value.trim(),
            cardNumber: form.querySelector('#card-number').value.trim(),
            expiryMonth: form.querySelector('#expiry-month').value,
            expiryYear: form.querySelector('#expiry-year').value,
            cvv: form.querySelector('#cvv').value.trim()
        };
    }

    // Validate payment form
    validatePaymentForm(data, container) {
        // Check required fields
        const requiredFields = [
            'firstName', 'lastName', 'email', 'phone', 'company', 
            'address', 'city', 'state', 'zip', 'country',
            'cardName', 'cardNumber', 'expiryMonth', 'expiryYear', 'cvv'
        ];

        for (const field of requiredFields) {
            if (!data[field]) {
                UIUtils.showError('Please fill in all required fields.', container);
                return false;
            }
        }

        // Validate email
        if (!this.isValidEmail(data.email)) {
            UIUtils.showError('Please enter a valid email address.', container);
            return false;
        }

        // Validate phone (basic check)
        if (data.phone.length < 10) {
            UIUtils.showError('Please enter a valid phone number.', container);
            return false;
        }

        // Validate card number (basic check - remove spaces and check length)
        const cardNumber = data.cardNumber.replace(/\s/g, '');
        if (cardNumber.length < 13 || cardNumber.length > 19) {
            UIUtils.showError('Please enter a valid card number.', container);
            return false;
        }

        // Validate CVV
        if (data.cvv.length < 3 || data.cvv.length > 4) {
            UIUtils.showError('Please enter a valid CVV.', container);
            return false;
        }

        // Validate expiry date
        const currentYear = new Date().getFullYear();
        const currentMonth = new Date().getMonth() + 1;
        const expiryYear = parseInt(data.expiryYear);
        const expiryMonth = parseInt(data.expiryMonth);

        if (expiryYear < currentYear || (expiryYear === currentYear && expiryMonth < currentMonth)) {
            UIUtils.showError('Please enter a valid expiry date.', container);
            return false;
        }

        return true;
    }

    // Email validation helper
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Store payment details for reference
    storePaymentDetails(formData, productId, paymentResponse) {
        const paymentDetails = {
            productId,
            customerInfo: {
                name: `${formData.firstName} ${formData.lastName}`,
                email: formData.email,
                phone: formData.phone,
                company: formData.company,
                address: {
                    street: formData.address,
                    city: formData.city,
                    state: formData.state,
                    zip: formData.zip,
                    country: formData.country
                }
            },
            paymentUrl: paymentResponse.payment_url,
            timestamp: new Date().toISOString()
        };

        localStorage.setItem('lastPaymentDetails', JSON.stringify(paymentDetails));
    }

    // Get stored payment details
    getStoredPaymentDetails() {
        const details = localStorage.getItem('lastPaymentDetails');
        return details ? JSON.parse(details) : null;
    }

    // Clear stored payment details
    clearStoredPaymentDetails() {
        localStorage.removeItem('lastPaymentDetails');
    }
}

// Initialize payment handler when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const paymentHandler = new PaymentHandler();
    
    // Make paymentHandler globally available for debugging
    window.paymentHandler = paymentHandler;

    // Add card number formatting
    const cardNumberInput = document.getElementById('card-number');
    if (cardNumberInput) {
        cardNumberInput.addEventListener('input', function(e) {
            // Remove all non-digits
            let value = e.target.value.replace(/\D/g, '');
            
            // Add spaces every 4 digits
            value = value.replace(/(\d{4})(?=\d)/g, '$1 ');
            
            // Limit to 19 characters (16 digits + 3 spaces)
            if (value.length > 19) {
                value = value.substring(0, 19);
            }
            
            e.target.value = value;
        });
    }

    // Add CVV validation
    const cvvInput = document.getElementById('cvv');
    if (cvvInput) {
        cvvInput.addEventListener('input', function(e) {
            // Only allow digits and limit to 4 characters
            e.target.value = e.target.value.replace(/\D/g, '').substring(0, 4);
        });
    }
});
