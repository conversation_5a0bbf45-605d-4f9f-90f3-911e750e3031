<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Payment Status - CureOx Healthcare Software</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <link href="styles/theme.css" rel="stylesheet">
</head>
<body>
  <!-- Navigation -->
  <nav class="bg-gray-900 bg-opacity-90 backdrop-filter backdrop-blur-lg fixed w-full z-10 shadow-lg">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <a href="index.html" class="text-2xl font-bold text-gradient flex items-center">
              <span class="mr-2">CureOx</span>
              <svg class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </a>
          </div>
        </div>
        <div class="hidden md:block">
          <div class="flex items-center space-x-4">
            <button id="theme-toggle" class="text-gray-300 hover:text-white focus:outline-none">
              <svg id="moon-icon" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
              </svg>
              <svg id="sun-icon" class="h-5 w-5 hidden" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
              </svg>
            </button>
            <a href="contact.html" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-all">
              Contact Us
            </a>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- Payment Status Section -->
  <section class="pt-32 pb-16 bg-gray-900 min-h-screen">
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="bg-gray-800 p-8 rounded-lg shadow-lg text-center">
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-6">
          <svg class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
          </svg>
        </div>
        
        <h1 class="text-3xl font-bold mb-4">Payment Processing</h1>
        <p class="text-gray-300 mb-8">Your payment is being processed. You should have been redirected to the payment gateway in a new tab.</p>
        
        <div class="space-y-4 mb-8">
          <div class="bg-blue-500 bg-opacity-20 border border-blue-400 text-blue-300 px-4 py-3 rounded">
            <div class="flex items-center justify-center">
              <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
              </svg>
              <span>If the payment window didn't open, please check your popup blocker settings.</span>
            </div>
          </div>
          
          <div id="payment-details" class="bg-gray-700 p-4 rounded-md text-left">
            <!-- Payment details will be populated by JavaScript -->
          </div>
        </div>
        
        <div class="space-y-4">
          <button id="open-payment" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium transition-all">
            Open Payment Gateway
          </button>
          
          <div class="flex space-x-4">
            <a href="index.html" class="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-md font-medium transition-all text-center">
              Return to Home
            </a>
            <a href="contact.html" class="flex-1 bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-md font-medium transition-all text-center">
              Contact Support
            </a>
          </div>
        </div>
        
        <!-- Status Check -->
        <div class="mt-8 p-4 bg-yellow-500 bg-opacity-20 border border-yellow-400 rounded-md">
          <div class="flex items-center justify-center">
            <svg class="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <div class="text-left">
              <p class="text-yellow-300 text-sm font-medium">Payment Status</p>
              <p class="text-yellow-200 text-sm">After completing payment, you'll receive a confirmation email. If you have any issues, please contact our support team.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gradient-to-r from-gray-900 to-blue-900 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <a href="index.html" class="text-2xl font-bold text-gradient">CureOx</a>
        <p class="mt-2 text-gray-300">Transforming healthcare with innovative software solutions.</p>
      </div>
      <div class="border-t border-gray-700 mt-6 pt-6 text-center text-gray-400">
        <p>© 2023 CureOx. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="js/api.js"></script>
  <script src="js/payment.js"></script>
  <script src="script.js"></script>
  <script src="js/user-preferences.js"></script>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Initialize AOS
      AOS.init();

      // Get payment details from localStorage
      const paymentDetails = window.paymentHandler ? window.paymentHandler.getStoredPaymentDetails() : null;
      const paymentDetailsContainer = document.getElementById('payment-details');
      const openPaymentButton = document.getElementById('open-payment');

      if (paymentDetails) {
        // Display payment details
        paymentDetailsContainer.innerHTML = `
          <h3 class="font-semibold mb-2">Payment Details</h3>
          <div class="space-y-1 text-sm text-gray-300">
            <p><strong>Product:</strong> ${paymentDetails.productId}</p>
            <p><strong>Customer:</strong> ${paymentDetails.customerInfo.name}</p>
            <p><strong>Email:</strong> ${paymentDetails.customerInfo.email}</p>
            <p><strong>Company:</strong> ${paymentDetails.customerInfo.company}</p>
            <p><strong>Date:</strong> ${new Date(paymentDetails.timestamp).toLocaleString()}</p>
          </div>
        `;

        // Set up open payment button
        openPaymentButton.addEventListener('click', function() {
          if (paymentDetails.paymentUrl) {
            window.open(paymentDetails.paymentUrl, '_blank');
          } else {
            alert('Payment URL not available. Please contact support.');
          }
        });
      } else {
        // No payment details found
        paymentDetailsContainer.innerHTML = `
          <div class="bg-red-500 bg-opacity-20 border border-red-400 text-red-300 px-4 py-3 rounded">
            <p class="font-medium">No payment details found</p>
            <p class="text-sm">Please return to the checkout page to complete your purchase.</p>
          </div>
        `;
        
        openPaymentButton.disabled = true;
        openPaymentButton.textContent = 'No Payment Available';
        openPaymentButton.className = 'w-full bg-gray-600 text-gray-400 px-6 py-3 rounded-md font-medium cursor-not-allowed';
      }

      // Auto-refresh payment status (in a real app, you'd check with your backend)
      let statusCheckCount = 0;
      const maxStatusChecks = 10;
      
      const checkPaymentStatus = setInterval(() => {
        statusCheckCount++;
        
        if (statusCheckCount >= maxStatusChecks) {
          clearInterval(checkPaymentStatus);
          return;
        }
        
        // In a real implementation, you would make an API call to check payment status
        console.log(`Checking payment status... (${statusCheckCount}/${maxStatusChecks})`);
      }, 30000); // Check every 30 seconds
    });
  </script>
</body>
</html>
